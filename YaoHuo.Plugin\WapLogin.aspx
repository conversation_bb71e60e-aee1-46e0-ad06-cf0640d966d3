﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="WapLogin.aspx.cs" Inherits="YaoHuo.Plugin.WapLogin" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    if (this.INFO == "OK")
    {
        wmlVo.timer = "0";
        wmlVo.strUrl = backurl;
    }
    StringBuilder strhtml = new StringBuilder();
        strhtml.Append("<!DOCTYPE html><html>");
        strhtml.Append("<head>");
        strhtml.Append("<meta charset='utf-8'><meta name=\"theme-color\" content=\"#378D8D\"><link rel=\"preload\" href=\"/NetCSS/CSS/Login/fonts/fontawesome-webfont.woff2?v=4.7.0\" as=\"font\" type=\"font/woff2\" crossorigin=\"anonymous\">");
        strhtml.Append("<meta http-equiv='X-UA-Compatible' content='IE=edge,chrome=1'>");
        strhtml.Append("<meta name='keywords' content='妖火,妖火网,妖火论坛'/>");
        strhtml.Append("<meta name='viewport' content='width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no'>");
        strhtml.Append("<title>登录 - 妖火网</title>");
        strhtml.Append("<meta http-equiv='Content-type' content='text/html;charset=UTF-8'>");
        strhtml.Append("<link rel='stylesheet' href='/NetCSS/CSS/Login/Style.css'>");
        strhtml.Append("<script src='/NetCSS/CSS/Login/Home.js'></script>");
        strhtml.Append("<script src='/NetCSS/CSS/Login/Zero.js'></script>");
        //底部引入资源文件
        if (this.CaptchaProvider == "cloudflare" && !string.IsNullOrEmpty(this.TurnstileSiteKey))
        {
            // 使用Cloudflare Turnstile验证码
            strhtml.Append("<script src='//challenges.cloudflare.com/turnstile/v0/api.js' async defer></script>");
        }
        else if (this.CaptchaProvider == "tencentcloud" && !string.IsNullOrEmpty(this.TencentCloudCaptchaAppId))
        {
            // 使用腾讯云验证码
            strhtml.Append("<script src='/NetCSS/CSS/Login/TJCaptcha.js'></script>");
        }
        else if (this.CaptchaProvider == "gocaptcha" && !string.IsNullOrEmpty(this.GoCaptchaAppId))
        {
            // 使用GoCaptcha验证码
            strhtml.Append("<script src='/NetCSS/CSS/Login/Gocaptcha/gocaptcha.global.js?60'></script><script src='/NetCSS/CSS/Login/Gocaptcha/gocaptcha-init.js?v38'></script>");
            strhtml.Append("<link href='/NetCSS/CSS/Login/Gocaptcha/gocaptcha.global.css?20' rel='stylesheet' />");
            strhtml.Append("<link href='/NetCSS/CSS/Login/Gocaptcha/gocaptcha-modal.css?20' rel='stylesheet' />");
            // 设置 Turnstile Site Key 全局变量，供备用验证码使用
            strhtml.Append("<script>window.TURNSTILE_SITE_KEY = '" + this.TurnstileSiteKey + "';</script>");
        }
        strhtml.Append("</head>");
        if (this.INFO == "OK")
        {
            strhtml.Append("<meta http-equiv='refresh' content='0;url=wapindex.aspx?sid=-2'><div class='wrap'><div style='text-align: center; color: #FFFFFF; font-weight: 500; padding-bottom: 10px; letter-spacing: 2px; margin-top: 120px;'><h1>登录成功</h1></div></div>");
        }
        if (this.INFO == "IDNULL")
        {
            strhtml.Append("<div class='tip'>");
            strhtml.Append("" + this.GetLang("请输入账号|请输入账号|username not null!") + "</div>");
        }
        else if (this.INFO == "PASSNULL")
        {
            strhtml.Append("<div class='tip'>");
            strhtml.Append("" + this.GetLang("请输入密码|请输入密码|password not null!") + "</div>");
        }
        else if (this.INFO == "NOTEXIST")
        {
            strhtml.Append("<div class='tip'>");
            strhtml.Append("" + this.GetLang("请确认账号正确|请确认账号正确|username not exist ") + "</div>");
        }
        else if (this.INFO == "PASSERR")
        {
            strhtml.Append("<div class='tip'>");
            strhtml.Append("" + this.GetLang("请确认密码正确|请确认密码正确|Password error!") + "</div>");
        }
        else if (this.INFO == "USERLOCK")
        {
            strhtml.Append("<div class='tip'>");
            strhtml.Append("" + this.GetLang("用户被锁定|用户被锁定|user locked!") + "</div>");
        }
        else if (this.INFO == "MAXLOGIN")
        {
            strhtml.Append("<div class='tip'>");
            strhtml.Append("" + this.GetLang("登录失败超过 " + this.KL_LoginTime + " 次了，请明天再来") + "</div>");
        }
        else if (this.INFO == "NOTHUMANVERIFY")
        {
            strhtml.Append("<div class='tip'>");
            strhtml.Append("" + this.GetLang("人机验证失败，请重试") + "</div>");
        }
        else if (this.INFO == "weixin")
        {
            strhtml.Append("<div class='tip'>");
            if (publicID != "")
            {
                strhtml.Append("请在微信加本站公共帐号:" + publicName + " 或 " + publicID + " ，关注后自动注册成为会员，更改密码在微信上发送：密码+XXXX，查看注册帐号信息发送：帐号");
            }
            else
            {
                strhtml.Append("本站管理员还没有配置微信共帐号。");
            }
            strhtml.Append("</div>");
        }
        if (errorinfo == "config")
        {
            strhtml.Append("<div class='tip'>");
            strhtml.Append("<b>如果总是进入登录界面，请联系站长处理</b></div>");
        }
        if (this.INFO.StartsWith("VERIFY_"))
        {
            strhtml.Append("<div class='tip'>");
            strhtml.Append("验证详情: " + this.INFO);
            strhtml.Append("</div>");
        }
        else if (this.INFO == "NOTHUMANVERIFY_TOKEN_NULL")
        {
            strhtml.Append("<div class='tip'>");
            strhtml.Append("" + this.GetLang("请完成人机验证后再提交") + "</div>");
        }
        if (this.INFO.StartsWith("VERIFY_ERROR:"))
        {
            strhtml.Append("<div class='tip'>");
            strhtml.Append("<details>");
            strhtml.Append("<summary>验证失败，点击查看详情</summary>");
            strhtml.Append("<pre style='white-space: pre-wrap;'>");
            strhtml.Append(this.INFO.Replace("VERIFY_ERROR:", ""));
            strhtml.Append("</pre>");
            strhtml.Append("</details>");
            strhtml.Append("</div>");
        }
        else if (this.INFO == "NOTHUMANVERIFY_RESPONSE_NULL")
        {
            strhtml.Append("<div class='tip'>");
            strhtml.Append("" + this.GetLang("验证服务无响应，请稍后重试") + "</div>");
        }
        string isWebHtml = this.ShowWEB_view(this.classid);
        if (this.INFO != "OK")
        {
            strhtml.Append("<body id='login' class='unloaded'><div class='wrapper'><div class='zero'></div><div class='login'> <form action='/waplogin.aspx' method='post' name='login' class='container offset1 loginform'> <div id='owl-login'> <div class='eyes'></div> <div class='arm-up-right'></div> <div class='arm-up-left'></div> <div class='arm-down-left'></div> <div class='arm-down-right'></div> </div><div class='pad'> <div class='control-group'> <div class='controls'> <label for='email' class='control-label fa fa-envelope'></label> <input type='text' name='logname' id='logname' placeholder='手机或ID号' tabindex='1' autofocus='autofocus' required class='form-control input-medium' autocomplete=\"username\" value='" + this.logname + "'/> </div> </div> <div class='control-group'> <div class='controls'> <label for='password' class='control-label fa fa-asterisk'> </label> <input id='password' type='password' name='logpass' placeholder='请输入密码' tabindex='2' required class='form-control input-medium' autocomplete=\"current-password\" value='" + this.logpass + "'/> <input type='hidden' name='action' value='login'><input type='hidden' name='classid' value='0'><input type='hidden' name='siteid' value='1000'><input type='hidden' name='backurl' value='" + backurl + "'/><input type='hidden' name='savesid' value='0'></div></div>");
            
            // 人机验证widget
            if (this.CaptchaProvider == "cloudflare" && !string.IsNullOrEmpty(this.TurnstileSiteKey))
            {
                // 使用Cloudflare Turnstile验证码
                strhtml.Append("<div class='cf-turnstile' " +
                               "data-sitekey='" + this.TurnstileSiteKey + "' " +
                               "data-theme='light' " +
                               "data-language='zh-cn'></div>");
            }
            else if (this.CaptchaProvider == "tencentcloud" && !string.IsNullOrEmpty(this.TencentCloudCaptchaAppId))
            {
                // 使用腾讯云验证码
                strhtml.Append("<div id='TencentCaptcha' style='margin: 10px 0;'></div>");
                strhtml.Append("<input type='hidden' name='tencent_ticket' id='tencent_ticket' value='' />");
                strhtml.Append("<input type='hidden' name='tencent_randstr' id='tencent_randstr' value='' />");
            }
            else if (this.CaptchaProvider == "gocaptcha" && !string.IsNullOrEmpty(this.GoCaptchaAppId))
            {
                // 使用GoCaptcha验证码
                strhtml.Append("<div id='gocaptcha-wrap' style='width: 100%; margin: 0 auto;'></div>");
                strhtml.Append("<input type='hidden' id='gocaptcha-token' name='gocaptchaToken' value='' />");
            }
            
            strhtml.Append("</div><div class='form-actions'> <a tabindex='5' href='/bbs-138352.html' class='btn pull-left btn-link text-muted'>注册</a> <button type='submit' tabindex='4' class='btn btn-primary'>登录</button> </div></form></div></div></html>");
            
            // 添加腾讯云验证码的内联脚本
            if (this.CaptchaProvider == "tencentcloud" && !string.IsNullOrEmpty(this.TencentCloudCaptchaAppId))
            {
                strhtml.Append(@"<script>

                document.addEventListener('DOMContentLoaded', function() {
                  var loginForm = document.forms['login'];
                  if(loginForm) {
                    loginForm.addEventListener('submit', function(event) {
                      var tencentTicket = document.getElementById('tencent_ticket').value;
                      if ('" + this.CaptchaProvider + @"' === 'tencentcloud' && !tencentTicket) {
                        event.preventDefault();
                        try {
                          var captcha = new TencentCaptcha('" + this.TencentCloudCaptchaAppId + @"', function(res) {
                            console.log(res);
                            if (res.ret === 0) {
                              document.getElementById('tencent_ticket').value = res.ticket;
                              document.getElementById('tencent_randstr').value = res.randstr;
                              loginForm.submit();
                            } else { /* User closed captcha */ }
                          }, {}); 
                          captcha.show();
                        } catch (error) { console.error('TencentCaptcha error:', error); }
                      }
                    });
                  }
                });
                </script>");
            }
            strhtml.Append("</body></html>");
        }
        if (isWebHtml != "")
        {
            Response.Clear();
            Response.Write(WapTool.ToWML(isWebHtml, wmlVo).Replace("[view]", strhtml.ToString()));
            Response.End();
        }
        Response.Write(strhtml);
%>