<?xml version="1.0" encoding="UTF-8"?>
<configuration>
<appSettings>

    <!--是否只有登录才可以访问网站页面，如需要请下面录入说明，否则留空-->
    <add key="KL_Mast_Login_INFO" value="" />

    <!--是否关闭会员活动日志信息(wap_log表)，会生成大量记录，1为关闭，空或0为写入-->
    <add key="KL_CLOSE_LOG_INFO" value="0" />

    <!--全局过滤字符-->
    <!--unicode|ascii|insert|delete|truncate|declare|create|eval|command-->
    <add key="KL_In" value="mymoney=|/addtopwap.aspx|/wapstyle" />
    <!--KL_In主要配置SQL注入的英文字符，此处的KL_Filter_All是过滤所有中文或其它字符。例：WML或电脑版写代码时是不能过滤KL_In中的字符的，但是要过滤以下非法字符（太多数可能是脚本或过滤广告代码）-->
    <add key="KL_Filter_All" value="display" />

    <!--关闭系统进提示信息，例此系统正在升级，预计几点到几点开放。-->
    <add key="KL_CloseWeb_Tip" value="" />
    
    <!--游戏六合彩是否关闭，0或空为关闭，1开启，建议关闭防移动封IP！-->
    <add key="KL_Games_Close_MarkSix" value="1" />
    
    <!--是否开启WEB排版功能，1为开启(如开启请配置KL_Filter_All过滤字符 和 KL_GoVersion3参数),0为关闭(当关闭或排版内容为空时默认进入WAP2.0页面)-->
    <add key="KL_Open_Web" value="1" />

    <!--是否开启"网站默认设置"中的[20]选电脑版(ver=3)时跳转[21]选平板电脑版(ver=4)时跳转，1为开，0为关，建议关，防非审核网站转发色站内容的地址-->
    <add key="KL_Open_Redirect" value="0" />

    <!--显示标题链接UBB:[功能模块标识=N_M_X_Y]中的X=5为随机所有，实际为以下最新多条进行随机，默认100-->
    <add key="KL_OrderByNew" value="100" />

    <!-- 0时开启全局防SQL注入；1时开启局部防SQL注入-->
    <add key="KL_In_Open" value="1" />

    <!--Session过期时间(分钟)，用于WEB后台-->
    <add key="KL_SessionTimeOut" value="60" />

    <!--防刷新，限制提交数据时间(秒)-->
    <add key="KL_CheckIPTime" value="0" />

    <!--在以下时间(分钟)内不活动的在线人，将会被删除-->
    <!--建议在此设为0，然后在企业管理器作业中定时执行:DELETE FROM fcount WHERE (DATEDIFF(hh, ftime, GETDATE()) > 1)-->
    <add key="KL_DelOnlineTime" value="100" />   

    <!--全局商店提交订单注册会员，0为关，1为开-->
    <add key="KL_SHOP_REG" value="0" />
    
    <!--网站访问详细数据，建议关闭，否则产生大量数据，数据库会暴掉。0为关，1为开-->
    <add key="KL_VisiteCount_Detail" value="0" /> 

    <!--是否要校验每个地址域名绑定，1要，0不需要(可提高性能)-->
    <add key="KL_CheckDomain" value="0" />

    <!--用域名访问时隐藏后面的参数，1隐藏，0显示-->
    <add key="KL_HiddenQuery" value="1" />

    <!--全站用户发帖限制-->
    <add key="KL_CheckBBSCount" value="3" />
	
	<!--指定用户发帖限制-->
	<add key="KL_SpecificUserBBSCount" value="1000:10,42498:1,22610:1,25253:1,30875:2,29006:2,25669:10,17101:5" />

	<!--排除新帖栏目-->
	<add key="KL_BBS_ExcludeClassIDs" value="299" />

    <!--每天只能发多少条回帖，0为不限制-->
    <add key="KL_CheckBBSreCount" value="200" />

    <!--每天只能发多少条站内短信，0为不限制-->
    <add key="KL_SendMSGCount" value="100" />

    <!--每天只能发多少条空间留言，0为不限制-->
    <add key="KL_CheckZoneReCount" value="30" />

     <!--每天只能加多少好友，0为不限制-->
    <add key="KL_ADDFriendCount" value="10" />

    <!--登录失败次数限制，防密码被破-->
    <add key="KL_LoginTime" value="0" />
	
    <!--WEB后台，网站管理，短信发送功能，每次添加操作时，最多添加多少个短信，默认500。-->
    <add key="KL_SendSMS_Add" value="500" />

    <!--[优先级0]强制进入WAP2.0-->
    <add key="KL_GoVersion0" value="Opera|SP|QQ|Mini|series" />

    <!--[优先级1]当移动网关不提供UA时将进入WAP1.0，建议留空-->
    <add key="KL_GoVersion1" value="" />

    <!--[优先级2]移动网关提供有UA情况下,进入平版触屏幕,一般留空由界面上选择进入-->
    <add key="KL_GoVersion4" value="" />

    <!--[优先级3]移动网关提供有UA情况下,进入电脑版-->
    <add key="KL_GoVersion3" value="Windows NT|MSIE|Firefox" />    

    <!--[优先级4]移动网关提供有UA情况下，当网站进入不同版本设为自动识别时(0)，有以下UA关键字时进入2.0界面-->
    <add key="KL_GoVersion2" value="Safari|iPhone|ios|Android|Opera|CE|UC|QQ|Phone|Mobile|Mozilla" />

    <!--[优先级1]下载防盗版参数一，1开启防盗功能，不会显示实际地址，文件名还会带上当前域名；0关闭，会显示实际地址。防盗参数，建议设为1 -->
    <add key="KL_DownCheck" value="1" />    

    <!--[优先级2]下载防盗版参数二，强制判断上一个访问页面(Request.UrlReferrer)是本站的，1开0关-->
    <add key="KL_DownCheckReferrer" value="0" />

    <!--下载是否开启多线程，且开启防盗链后，1开启，0关闭-->
    <add key="KL_DownThread" value="1" />

    <!--开启单线程下载后，且开启防盗链后，每秒允许下载的字节数，如102400 =100K-->
    <add key="KL_DownSpeed" value="2048000" />

    <!--强制不允许上传和下载的扩展名-->
    <add key="KL_NotDownAndUpload" value="ad|adprototype|asa|asax|ascx|ashx|asmx|axd|browser|cd|cdx|cer|compiled|confi|cs|csproj|dd|exclude|idc|java|jsl|ldb|ldd|lddprototype|ldf|licx|master|mdb|mdf|msgx|refresh|rem|resources|resx|sd|sdm|sdmdocument|shtm|shtml|sitemap|skin|soap|stm|svc|vb|vbproj|vjsproj|vsdisco|webinfo|wml|asp|jsp|asa|asax|cer|cdx|htr|php|aspx|shtml|config|exe|mdb" />

    <!--设置下载防盗后启用自定义mine类型，程序内置手机常用mine格式了，如果不能下载请自行设置-->    
    <!--注意：如果你下载的地址是URL实际地址，那你需要在主机IIS或空间管理后台添加Mine类型！！！-->
    <!--[优先级1]在下面配置找；[优先级2]在程序内置找；[优先级3]以上找不到默认:application/octet-stream-->
    <!--格式如下，可自行增加-->
    <add key="KL_MINE_3gp" value="video/3gpp" />
    <add key="KL_MINE_sis" value="application/vnd.symbian.install" />
    
    <!--开启防盗链下载时，有些浏览器需要对文件名编码才不会乱码，以下UA下载将对文件名编码-->
    <add key="KL_FileNameURLencode" value="Mozilla|MSIE" />

    <!--当站长在基本信息设置按UA只能手机访问时，以下UA将被允许-->
    <add key="KL_Allow_UA" value="UC|SP" />

    <!--当站长在基本信息设置只能手机访问时，以下IP段将被允许，目前只配了移动和联通IP段，其它的请自行加入，格式参考:IP1-IP2|IP3-IP4用-和|区别-->
    <add key="KL_Allow_IP" value="***********-***************|***********-***************|***********-***************|***********-***************|**********-**************|**********-**************|***********-***************|**********-**************|**********-**************" />

    <!-- 当系统抛出异常错误信息时，当KL_ShowAllError设为1时，显示所有详细错误，为0时，只显示第一行错误信息-->
    <add key="KL_ShowAllError" value="0" />

    <!-- 开启全文索引，搜索标题，需要专业技术员操作。0关闭，1开启。-->
    <add key="KL_FULLSEARCH_article" value="0" />
    <add key="KL_FULLSEARCH_bbs" value="0" />
    <add key="KL_FULLSEARCH_download" value="0" />
    <add key="KL_FULLSEARCH_picture" value="0" />
    <add key="KL_FULLSEARCH_ring" value="0" />
    <add key="KL_FULLSEARCH_video" value="0" />

    <!--关闭第三级页面上一条，下一条显示，可提升性能，加快页面显示,0为开，1为关-->
    <add key="KL_ShowPreNextTitle_bbs" value="1" />
    <add key="KL_ShowPreNextTitle_download" value="1" />
    <add key="KL_ShowPreNextTitle_article" value="1" />

    <!--多用户系统中防止站长过滤系统设置的广告。0关闭，1开启-->
    <add key="KL_Kill_None" value="0" />

    <!--WAP界面注册普通会员验证码:0为文本数字，1为图片数字，手机WAP可能不支持图片session保存数字，但图片数字起到防刷作用-->
    <add key="KL_RegUser_CheckCode" value="1" />

    <!--WEB关闭校验码功能,1为关闭,0为开启-->
    <add key="KL_WEB_RegUser_CheckCode" value="0" />

    <!--WAP界面注册站长验证码:0为文本数字，1为图片数字，手机WAP可能不支持图片session保存数字，但图片数字起到防刷作用-->
    <add key="KL_RegSite_CheckCode" value="0" />

    <!--主机开启了CDN功能的，取CDN用户真实IP的方法，1为开启,0为默认-->
    <add key="KL_GetIP_FromCDN" value="1" />

    <!--WAP管理后台首次进入是否需要密码,0要,1不需要-->
    <add key="KL_WAPAdmin_NeedPassWord" value="0" />

    <!--IP转省市功能，流量统计和天气预报用到，关闭可以优化性能。0为开启,1为关闭-->
    <add key="KL_Close_IPtoCity" value="1" />

    <!--bbs论坛模块下载文件时间小于或等于设定值时，下载地址将转向设定的地址。格式:20110830|http://bbs.kelink.com/bbs/  不用留空 -->
    <add key="KL_JUMPURL_bbs" value="" />

    <!--download下载模块下载文件时间小于或等于设定值时，下载地址将转向设定的地址。格式:20110830|http://dl.kelink.com/download/ 不用留空-->
    <add key="KL_JUMPURL_download" value="" />

    <!--自定义UBB文字链接的开头地址！如http://kelink.com|http://3g.kelink.com-->
    <add key="KL_URL_FILTER" value="" />

    <!--自定义UBB图片链接的开头地址！-->
    <add key="KL_IMG_FILTER" value="" />

    <!--底层审核图片/文件，地址带有upload和uploadfiles目录下文件，1开启,0关闭，此功能需要在IIS属性中添加映射。请联系技术员或看底层文件审核设置帮助文档-->
    <add key="KL_FileCheck" value="1" />    

    <!--底层防盗，判断Request.UrlReferrer，1开启,0关闭-->
    <add key="KL_FileCheck_UrlReferrer" value="0" />

    <!--是否开启网站缓存功能，1开启,0关闭-->
    <add key="KL_OpenCache" value="0" />  

    <!--缓存保存多长时间,0不主动清除，1为1小时,2为2小时...-->
    <add key="KL_OpenCacheTime" value="1" />

    <!--注册和修改昵称时是否效验唯一性，0开启；1为关闭，可提高性能-->
    <add key="KL_Check_Repeat_Nickname" value="0" />

    <!--开启伪静态地址重写功能，0关闭，1开启，具体配置看《伪静态插件安装说明ISAPI_Rewrite3》下的说明-->
    <add key="ISAPI_Rewrite3_Open" value="1" />

    <!--论坛是否开放游客发帖，0关闭，1开放，优先二在版务，更多栏目属性【11】配置-->
    <add key="KL_BBS_Anonymous_Open" value="0" />

    <!--论坛回复是否开放游客回帖，0关闭，1开放，优先二在版务，更多栏目属性【12】配置-->
    <add key="KL_BBSRE_Anonymous_Open" value="0" />

    <!--文章回复是否开放游客评论，0关闭，1开放，优先级二更多栏目属性【2】配置-->
    <add key="KL_ARTRE_Anonymous_Open" value="0" />

    <!--视频模块回复是否开放游客评论，0关闭，1开放，优先级二更多栏目属性【2】配置-->
    <add key="KL_VIDRE_Anonymous_Open" value="0" />

    <!--图片模块回复是否开放游客评论，0关闭，1开放，优先级二更多栏目属性【1】配置-->
    <add key="KL_PICRE_Anonymous_Open" value="0" />

    <!--铃声模块回复是否开放游客评论，0关闭，1开放，优先级二更多栏目属性【2】配置-->
    <add key="KL_RINRE_Anonymous_Open" value="0" />

    <!--下载模块回复是否开放游客评论，0关闭，1开放，优先级二更多栏目属性【1】配置-->
    <add key="KL_DOWRE_Anonymous_Open" value="0" />

    <!--小说回复是否开放游客评论，0关闭，1开放，优先二在版务，更多栏目属性【12】配置-->
    <add key="KL_NOVRE_Anonymous_Open" value="0" />

    <!--============以下参数相反================-->

    <!--留言模块回复是否开放游客评论，0开启，1关闭，优先级二更多栏目属性【1】配置-->
    <add key="KL_GUERE_Anonymous_Open" value="1" />

    <!--留言是否开放游客发表，0开启，1关闭，优先级二更多栏目属性【0】配置-->
    <add key="KL_GUE_Anonymous_Open" value="1" />

    <!--商店是否开放游客评论，0开启，1关闭，优先级二更多栏目属性【0】配置-->
    <add key="KL_SHORE_Anonymous_Open" value="1" />


    <!--***********************以下信息很重要，需要安全保密*******************************-->

    <!--邮件发送smtp地址,例smtp.qq.com 还需要登录QQ邮箱后台，邮箱设置，帐户，开启IMAP/SMTP服务 -->
		<add key="KL_SMTP_ADRRESS" value="" />
		<!--邮件用户名,例85403498-->
		<add key="KL_SMTP_UID" value="85403498" />
		<!--邮件密码,例12345678-->
		<add key="KL_SMTP_PW" value="12345678" />
		<!--我的邮件地址，上面帐号对应的邮件地址,例***************-->
		<add key="KL_SMTP_EMAIL" value="<EMAIL>" />

    <!--///////////////////////////全局信息广告内容///////////////////////////-->
    <!--所有网站页面顶部内容，支持UBB-->
    <add key="KL_PAGE_TOP" value="" />
    <!--所有网站页面底部内容，支持UBB-->
    <add key="KL_PAGE_DOWN" value=" " />

    <!--所有非VIP网站页面顶部内容，支持UBB-->
    <add key="KL_PAGE_NOTVIP_TOP" value="" />
    <!--所有非VIP网站页面底部内容，支持UBB-->
    <add key="KL_PAGE_NOTVIP_DOWN" value="" />
    <!--顶和低部广告CSS样式名，为空默认:sysad+随机数，但仍可能被高手隐藏，建议改成content，content是显示内容的的样式-->
    <add key="KL_CSS_DIV_SYSAD_Name" value="" />

    <!--///////////////////////////数据库配置信息/////////////////////////////-->    
    <!--输入数据库IP或数据库服务名-->
    <add key="KL_SQL_SERVERIP" value="(local)" />
    <!--输入登录数据库用户名-->
    <add key="KL_SQL_UserName" value="" />
    <!--输入登录数据库密码-->
    <add key="KL_SQL_PassWord" value="" />
    <!--输入主数据库名-->
    <add key="KL_DatabaseName" value="" />

    <!--存放历史数据的数据库连接串(可以理解为连接备份数据库)，如论坛bbs/book_list_bak.aspx和bbs/book_view_bak.aspx，体现在版务中，能访问到备份表数据-->
    <!--参数值为：database=1;server=(local);uid=sa;pwd=888888-->
    <add key="KL_BAK_SQLCONN" value="" />
    <!--///////////////////////////数据库结束配置/////////////////////////////-->

    <!--超级管理员的伪验证码，WEB后台登录中"验证码"，WAP后台"第一次需要录入我的密码"需要输入此值，达到更高安全级别。可留空-->
    <add key="KL_CHECKCODE" value="zTvhcVqDBaQ25zBDYCic" />

    <!--多用户版，开通所有子站的短信注册功能，此处录入短信通道号码，多个手机号码用|区别，不用留空-->
    <add key="KL_SMS_ALLREG" value="" />
    <!--上面开通所有子站短信注册，接收地址中的效验码值-->
    <add key="KL_SMS_ALLREG_CheckCode" value="" />
    <!--如果是用柯林短信拖管，在此录入柯林短信猫产品ID-->
    <add key="KL_SMS_ALLREG_ID" value="" />
    <!--校验是否来自柯林短信中心,0进行校验,1不校验-->
    <add key="KL_SMS_IS_KELINK" value="1" />

    <!--授权码-->
    <add key="KL_License" value="Ezb2J4kGspNFV6+vNvwV5TiNe/Ahl7/DWGFkwggR3eTEHErVvrJWtxOxTfDSGsB4ROpjZDSs6pOvDXhPlFrSuCn+revjGAgSw0x4s/nSlQc+60l2uN/4yrMJrtuiBewc4gKaiQkwlahhLAnoK7rbCiO7bct2vauAdSBHPWjcN30=dBKU2vTN7Gx/1cpAmt9bs2FEW2AWKAOzQZKX6inO0V32xzkNY/RBv/vBctuemlEOtpGBfJ7Fg5/22nBNuJVAS3JQ1m1XJ9I7/MzuswBAhThycl3FCbxQgJfRoHQ4MvO4uwlEcil+wVm0FpZXrQrGDQ==" />

    <!--以下默认参数不能改-->
    <add key="InstanceName" value="kelinkWAP_Check" />

	<!--当前启用的验证码服务: None, Cloudflare, TencentCloud, GoCaptcha -->
	<add key="CaptchaProvider" value="GoCaptcha" />

	<!-- Cloudflare Turnstile 配置-->
	<add key="CloudflareTurnstileSiteKey" value="0x4AAAAAAA2TqWOTWYygNo4a"/>
	<add key="CloudflareTurnstileSecretKey" value="0x4AAAAAAA2TqTVZxDGwpG1NsqsQ8yqKUPI"/>

	<!-- GoCaptcha 服务配置 -->
	<add key="GoCaptchaEnabled" value="1" />
	<add key="GoCaptchaServiceUrl" value="http://localhost:8080" />
	<add key="GoCaptchaApiKey" value="my-secret-key-123" />

  	<!--腾讯云验证码配置-->
  	<add key="TencentCloudCaptchaAppId" value="190792057" />
  	<add key="TencentCloudCaptchaAppSecretKey" value="DfGEjdDVTI5iMzosKczxpgdNj" />
  	<add key="TencentCloudSecretId" value="AKIDkX8btypeO1tnX5RYSAg2A8yb0B8WheNt" />
  	<add key="TencentCloudSecretKey" value="MUO5oT0PaYO20lLhtxpTuNo4GWoutgHe" />

	<!--禁止使用的昵称-->
	<add key="ForbiddenNicknames" value="clover,|clove|over,cl0ver,clevor,Cl0ver,Glover,Clovre,Iover,习近平,习大大,习仲勋,毛习东,毛泽东,Ιover" />

</appSettings>

	<connectionStrings>
     <add name="kelinkWAP_CheckConnectionString1" connectionString="Data Source=ITSERVICE;Initial Catalog=kelinkWAP_Check;Persist Security Info=True;User ID=sa;MultipleActiveResultSets=False;Packet Size=4096;Application Name=&quot;Microsoft SQL Server Management Studio&quot;" providerName="System.Data.SqlClient" />
	</connectionStrings>

<system.web>

	<!--允许上传文件大小及连接超时时间，4.0请求验证改成2.0验证方式-->
    <httpRuntime maxRequestLength="102400" executionTimeout="300" requestValidationMode="2.0" />
	<!--是否开启请求验证机制-->
    <pages validateRequest="false" />

	<!-- 是否开启跳转到错误页，改为 Off 则显示详细错误信息 -->
    <customErrors defaultRedirect="/Pages/404.htm" mode="On">
    </customErrors>
	
	<!--控制 ASP.NET 是否在调试模式下运行-->
    <compilation debug="false">
        <assemblies>
            <add assembly="System.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        </assemblies>
    </compilation>

	<!-- 身份验证模式-->
    <authentication mode="Windows" />
	
	<!-- 指定应用程序的字符编码、请求和响应的编码方式-->
	<!-- <globalization fileEncoding='UTF-8' requestEncoding='UTF-8' responseEncoding='UTF-8' culture='zh-CN'/>-->
	
	<!-- 底层审核图片/文件的类型，可自行配置 -->
    <!-- <httpHandlers>
      <add verb="*" path="*.jpg,*.jpeg,*.gif,*.png,*.webp" type="KeLink.Com.ImgProtectHadler"/>
    </httpHandlers> -->

</system.web>


	<!-- 在 Internet 信息服务 7.0 下运行 ASP.NET AJAX 需要 system.webServer -->
	<system.webServer>
	
        <staticContent>
            <mimeMap fileExtension=".webp" mimeType="image/webp" />
            <mimeMap fileExtension=".py" mimeType="application/x-python" />
            <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="30.00:00:00" cacheControlCustom="public" />
        </staticContent>
		
		<defaultDocument>
			<files>
                <clear />
                <add value="wapindex.aspx" />
                <add value="default.aspx" />
                <add value="Default.htm" />
                <add value="index.aspx" />
                <add value="index.html" />
                <add value="index.htm" />
			</files>
		</defaultDocument>

		<rewrite>
			<rules>
				<rule name="YaohuoRefererCheck" stopProcessing="true">
					<match url="^(bbs/upload/|album/upload/).*" />
					<conditions>
						<!-- 只检查非空referer，如果referer存在则必须是yaohuo.me域名 -->
						<add input="{HTTP_REFERER}" pattern="^https?://" />
						<add input="{HTTP_REFERER}" pattern="^https?://(www\.|wap\.)?yaohuo\.me/.*" negate="true" />
					</conditions>
					<action type="CustomResponse" statusCode="403" statusReason="Forbidden" statusDescription="禁止访问 - 仅允许来自yaohuo.me的访问" />
				</rule>
				<rule name="portal_1">
					<match url="^(.*/)*wapindex-([0-9]+)-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/wapindex.aspx?siteid={R:2}&amp;classid={R:3}&amp;{R4}" />
				</rule>
				<rule name="portal_2">
					<match url="^login\.html\?*(.*)$" />
					<action type="Rewrite" url="waplogin.aspx?{R:1}" />
				</rule>
				<rule name="portal_3">
					<match url="^(.*/)*myfile-([0-9]+)-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/myfile.aspx?siteid={R:2}&amp;classid={R:3}&amp;{R4}" />
				</rule>
				<rule name="portal_bbs_1">
					<match url="^(.*/)*bbs-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/bbs/view.aspx?id={R:2}&amp;{R:3}" />
				</rule>
				<rule name="portal_bbs_2">
					<match url="^(.*/)*bbslist-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/bbs/list.aspx?classid={R:2}&amp;{R:3}" />
				</rule>
				<rule name="portal_bbs_3">
					<match url="^(.*/)*bbslist-([0-9]+)-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/bbs/list.aspx?classid={R:2}&amp;page={R:3}&amp;{R:4}" />
				</rule>
				<rule name="portal_bbs_4">
					<match url="^(.*/)*bbsre-([0-9]+)-([0-9]+)-([0-9]+)-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/bbs/book_re.aspx?siteid={R:2}&amp;classid={R:3}&amp;id={R:4}&amp;page={R:5}&amp;{R:6}" />
				</rule>
				<rule name="Redirect_AdLink_NonFriendlyUrls" stopProcessing="true">
					<match url="^adlink/(.*)" />
					<conditions>
						<add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
						<add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
					</conditions>
					<action type="Redirect" url="/Pages/AdLink/{R:1}" redirectType="Permanent" />
				</rule>
				<rule name="portal_adlink_1">
					<match url="^(.*/)*link-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/Pages/AdLink/view.aspx?id={R:2}&amp;{R:3}" />
				</rule>
				<rule name="portal_adlink_2">
					<match url="^(.*/)*adlinklist-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/Pages/AdLink/list.aspx?classid={R:2}&amp;{R:3}" />
				</rule>
				<rule name="portal_adlink_3">
					<match url="^(.*/)*adlinklist-([0-9]+)-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/Pages/AdLink/list.aspx?classid={R:2}&amp;page={R:3}&amp;{R:4}" />
				</rule>
				<rule name="portal_adlink_4">
					<match url="^(.*/)*adlinkre-([0-9]+)-([0-9]+)-([0-9]+)-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/Pages/AdLink/book_re.aspx?siteid={R:2}&amp;classid={R:3}&amp;id={R:4}&amp;page={R:5}&amp;{R:6}" />
				</rule>
				<rule name="portal_album_1">
					<match url="^(.*/)*album-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/album/view.aspx?id={R:2}&amp;{R:3}" />
				</rule>
				<rule name="portal_album_2">
					<match url="^(.*/)*albumlist-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/album/list.aspx?classid={R:2}&amp;{R:3}" />
				</rule>
				<rule name="portal_album_3">
					<match url="^(.*/)*albumlist-([0-9]+)-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/album/list.aspx?classid={R:2}&amp;page={R:3}&amp;{R:4}" />
				</rule>
				<rule name="portal_album_4">
					<match url="^(.*/)*albumre-([0-9]+)-([0-9]+)-([0-9]+)-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/album/book_re.aspx?siteid={R:2}&amp;classid={R:3}&amp;id={R:4}&amp;page={R:5}&amp;{R:6}" />
				</rule>
				<rule name="portal_wml_1">
					<match url="^(.*/)*wml-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/wml/view.aspx?id={R:2}&amp;{R:3}" />
				</rule>
				<rule name="portal_wml_2">
					<match url="^(.*/)*wmllist-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/wml/list.aspx?classid={R:2}&amp;{R:3}" />
				</rule>
				<rule name="portal_wml_3">
					<match url="^(.*/)*wmllist-([0-9]+)-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/wml/list.aspx?classid={R:2}&amp;page={R:3}&amp;{R:4}" />
				</rule>
				<rule name="portal_wml_4">
					<match url="^(.*/)*wmlre-([0-9]+)-([0-9]+)-([0-9]+)-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/wml/book_re.aspx?siteid={R:2}&amp;classid={R:3}&amp;id={R:4}&amp;page={R:5}&amp;{R:6}" />
				</rule>
				<rule name="portal_xinzhang_1">
					<match url="^(.*/)*xinzhang-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/xinzhang/view.aspx?id={R:2}&amp;{R:3}" />
				</rule>
				<rule name="portal_xinzhang_2">
					<match url="^(.*/)*xinzhanglist-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/xinzhang/list.aspx?classid={R:2}&amp;{R:3}" />
				</rule>
				<rule name="portal_xinzhang_3">
					<match url="^(.*/)*xinzhanglist-([0-9]+)-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/xinzhang/list.aspx?classid={R:2}&amp;page={R:3}&amp;{R:4}" />
				</rule>
				<rule name="portal_xinzhang_4">
					<match url="^(.*/)*xinzhangre-([0-9]+)-([0-9]+)-([0-9]+)-([0-9]+).html\?*(.*)$" />
					<action type="Rewrite" url="{R:1}/xinzhang/book_re.aspx?siteid={R:2}&amp;classid={R:3}&amp;id={R:4}&amp;page={R:5}&amp;{R:6}" />
				</rule>
				<rule name="WWW Redirect" stopProcessing="true">
					<match url=".*" />
					<conditions>
						<add input="{HTTP_HOST}" pattern="^yaohw.com$" />
					</conditions>
					<action type="Redirect" url="https://yaohuo.me/{R:0}" redirectType="Permanent" />
				</rule>
				<rule name="RedirectFaceToBbsFace" stopProcessing="true">
					<match url="^face/(.*)" />
					<action type="Rewrite" url="bbs/face/{R:1}" />
				</rule>
				<rule name="RedirectNetImages" stopProcessing="true">
					<match url="^NetImages/(.*)" />
					<action type="Rewrite" url="NetCSS/IMG/NetImages/{R:1}" />
				</rule>
				<rule name="RedirectTupian" stopProcessing="true">
					<match url="^Tupian/(.*)" />
					<action type="Rewrite" url="NetCSS/IMG/Tupian/{R:1}" />
				</rule>
				<rule name="RedirectClan" stopProcessing="true">
					<match url="^Clan/(.*)" />
					<action type="Rewrite" url="Games/Clan/{R:1}" />
				</rule>
				<rule name="RedirectTuChuang" stopProcessing="true">
					<match url="^TuChuang/(.*)" />
					<action type="Rewrite" url="Pages/TuChuang/{R:1}" />
				</rule>
				<rule name="RedirectBlog" stopProcessing="true">
					<match url="^Blog/(.*)" />
					<action type="Rewrite" url="Pages/Blog/{R:1}" />
				</rule>
				<rule name="RedirectChinabankWAP" stopProcessing="true">
					<match url="^Chinabank_WAP/(.*)" />
					<action type="Rewrite" url="Pages/Chinabank_WAP/{R:1}" />
				</rule>
				<rule name="RedirectClass" stopProcessing="true">
					<match url="^Class/(.*)" />
					<action type="Rewrite" url="Admin/Class/{R:1}" />
				</rule>
				<rule name="RedirectVisiteCount" stopProcessing="true">
					<match url="^VisiteCount/(.*)" />
					<action type="Rewrite" url="Admin/VisiteCount/{R:1}" />
				</rule>
				<rule name="RedirectWapStyle" stopProcessing="true">
					<match url="^WapStyle/(.*)" />
					<action type="Rewrite" url="Admin/WapStyle/{R:1}" />
				</rule>
				<rule name="RedirectWEB" stopProcessing="true">
					<match url="^WEB/(.*)" />
					<action type="Rewrite" url="Admin/WEB/{R:1}" />
				</rule>
				<rule name="RedirectHtmlFiles" stopProcessing="true">
					<match url="^(Go|HongBao|KA)\.html$" />
					<action type="Rewrite" url="Pages/{R:1}.html" />
				</rule>
				<rule name="RedirectFavicon" stopProcessing="true">
					<match url="^favicon\.ico$" />
					<action type="Rewrite" url="NetCSS/IMG/Favicon.ico" />
				</rule>
				<rule name="RedirectRobots" stopProcessing="true">
					<match url="^robots\.txt$" />
					<action type="Rewrite" url="Pages/Robots.txt" />
				</rule>
				<rule name="RedirectAds" stopProcessing="true">
					<match url="^ads\.txt$" />
					<action type="Rewrite" url="Pages/Ads.txt" />
				</rule>
				<rule name="RedirectSearch" stopProcessing="true">
					<match url="^Search/(.*)" />
					<action type="Rewrite" url="Pages/Search/{R:1}" />
				</rule>
				<rule name="RedirectTemplateDefault" stopProcessing="true">
					<match url="^Template/default/(.*)" />
					<action type="Rewrite" url="NetCSS/CSS/Index/Default/{R:1}" />
				</rule>
				<rule name="RewriteDefaultAndIndexToWapIndex" stopProcessing="true">
				  <match url="^(Default|Index)\.aspx$|^index\.html$" />
				  <action type="Rewrite" url="wapindex.aspx" appendQueryString="true" />
				</rule>
				<rule name="RewriteBbsIndexToBbsDefault" stopProcessing="true">
				  <match url="^bbs/index\.html$" />
				  <action type="Rewrite" url="bbs/default.aspx" appendQueryString="true" />
				</rule>
			</rules>
		</rewrite>

    <httpErrors errorMode="DetailedLocalOnly" defaultResponseMode="File">
    <remove statusCode="404" />
    <error statusCode="404" path="/Pages/404.htm" responseMode="ExecuteURL" />
    </httpErrors>

    <caching>
        <profiles>
         <add extension=".png" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" />
         <add extension=".gif" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" />
         <add extension=".jpg" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" />
         <add extension=".jpeg" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" />
         <add extension=".webp" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" />
        </profiles>
    </caching>

    <httpProtocol>
        <customHeaders>
            <remove name="Vary" />
            <add name="Vary" value="User-Agent" />
        	<add name="X-Frame-Options" value="sameorigin" />
			<remove name="Permissions-Policy" />
			<add name="Permissions-Policy" value="run-ad-auction=(), join-ad-interest-group=()" />
        </customHeaders>
    </httpProtocol>

    <urlCompression doStaticCompression="true" doDynamicCompression="true" />

<security>
    <requestFiltering>
        <!-- 限制上传文件大小为 100MB (104857600 字节) -->
        <requestLimits maxAllowedContentLength="104857600" />
        
        <!-- 添加 HTTP 方法限制 -->
        <verbs allowUnlisted="true">
            <add verb="OPTIONS" allowed="false" />
        </verbs>
    </requestFiltering>
</security>

	</system.webServer>
	<system.codedom>
		<compilers>
			<compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
			<compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
		</compilers>
	</system.codedom>

</configuration>